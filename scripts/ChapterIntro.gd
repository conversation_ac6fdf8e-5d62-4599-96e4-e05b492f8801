extends Control

@onready var video_player: VideoStreamPlayer = $VideoPlayer
@onready var chapter_title: Label = $TextContainer/ChapterTitle
@onready var chapter_description: Label = $TextContainer/ChapterDescription
@onready var fade_overlay: ColorRect = $FadeOverlay
@onready var audio_player: AudioStreamPlayer = $AudioPlayer

var current_chapter: int = 1
var is_typing: bool = false
var typing_speed: float = 0.05

# Texty pre kapitoly
var chapter_data = {
	1: {
		"title": "KAPITOLA 1: CESTA NA ZÁMOK",
		"description": "Kočiar ťa nesie do srdca Karpát - k záhade, ktorá nedá Van Helsingovi spať.",
		"video": "res://assets/kapitoly_videa/1.ogv"
	},
	2: {
		"title": "KAPITOLA 2: BRÁNA ZÁMKU",
		"description": "Pred tebou sa týči mohutná brána. Každý erb na nej skrýva príbeh moci a pádu.",
		"video": "res://assets/kapitoly_videa/2.ogv"
	},
	3: {
		"title": "KAPITOLA 3: PÁTRANIE V ZÁMKU",
		"description": "Vstupuješ do haly plnej tajomstiev. Van Helsing zanechal stopy svojho pátrania.",
		"video": "res://assets/kapitoly_videa/3.ogv"
	},
	4: {
		"title": "KAPITOLA 4: TAJNÉ KRÍDLO",
		"description": "Staré krídlo zámku skrýva alchymistické laboratórium a nebezpečné pasce.",
		"video": "res://assets/kapitoly_videa/4.ogv"
	},
	5: {
		"title": "KAPITOLA 5: KRYPTY",
		"description": "Zostupuješ do hlbín zámku, kde čaká pradávne zlo v kamenných katakombách.",
		"video": "res://assets/kapitoly_videa/5.ogv"
	},
	6: {
		"title": "KAPITOLA 6: KONFRONTÁCIA",
		"description": "Konečne stojíš pred sarkofágom. Isabelle Báthoryová sa prebúdza po storočiach.",
		"video": "res://assets/kapitoly_videa/6.ogv"
	},
	7: {
		"title": "KAPITOLA 7: EPILÓG",
		"description": "Van Helsing je zachránený, ale cesta k úplnému uzdraveniu iba začína.",
		"video": "res://assets/kapitoly_videa/7.ogv"
	}
}

func _ready():
	# Skryj text na začiatku
	chapter_title.modulate.a = 0.0
	chapter_description.modulate.a = 0.0
	chapter_description.text = ""

func start_chapter_intro(chapter_number: int):
	"""Spustí intro pre danú kapitolu"""
	current_chapter = chapter_number
	print("🎬 Spúšťam intro pre kapitolu ", chapter_number)
	
	if not chapter_data.has(chapter_number):
		print("❌ Chyba: Neexistujúce dáta pre kapitolu ", chapter_number)
		_finish_intro()
		return
	
	var data = chapter_data[chapter_number]
	
	# Nastaví titulok
	chapter_title.text = data.title
	
	# Načíta a spustí video
	var video_stream = load(data.video)
	if video_stream:
		video_player.stream = video_stream
		video_player.play()
		
		# Pripojí signál pre koniec videa
		if not video_player.finished.is_connected(_on_video_finished):
			video_player.finished.connect(_on_video_finished)
		
		print("✅ Video načítané: ", data.video)
	else:
		print("❌ Chyba načítania videa: ", data.video)
		_finish_intro()
		return
	
	# Spustí animáciu
	_start_intro_animation(data.description)

func _start_intro_animation(description_text: String):
	"""Spustí animáciu intro s fade efektmi a typewriter textom"""
	var tween = create_tween()
	tween.set_parallel(true)
	
	# 1. Fade in z čiernej (1s)
	tween.tween_property(fade_overlay, "color:a", 0.0, 1.0)
	
	# 2. Po 1.5s zobraz titulok (0.8s fade in)
	tween.tween_delay(1.5)
	tween.tween_property(chapter_title, "modulate:a", 1.0, 0.8)
	
	# 3. Po 2.5s začni typewriter efekt pre popis
	await get_tree().create_timer(2.5).timeout
	_start_typewriter_effect(description_text)

func _start_typewriter_effect(text: String):
	"""Spustí typewriter efekt pre popis kapitoly"""
	is_typing = true
	chapter_description.modulate.a = 1.0
	chapter_description.text = ""
	
	print("⌨️ Spúšťam typewriter efekt: ", text)
	
	for i in range(text.length()):
		if not is_typing:
			break
			
		chapter_description.text = text.substr(0, i + 1)
		await get_tree().create_timer(typing_speed).timeout
	
	is_typing = false
	print("✅ Typewriter efekt dokončený")

func _on_video_finished():
	"""Callback po dokončení videa"""
	print("🎬 Video dokončené, končím intro")
	_finish_intro()

func _finish_intro():
	"""Dokončí intro a prejde na kapitolu"""
	print("🎬 Dokončujem intro pre kapitolu ", current_chapter)
	
	# Fade out do čiernej
	var tween = create_tween()
	tween.tween_property(fade_overlay, "color:a", 1.0, 1.0)
	
	await tween.finished
	
	# Prejdi na kapitolu
	_load_chapter()

func _load_chapter():
	"""Načíta príslušnú kapitolu"""
	print("📖 Načítavam kapitolu ", current_chapter)
	
	# Nastaví GameManager
	if GameManager:
		GameManager.current_chapter = current_chapter
		GameManager.story_phase = 0
	
	# Načíta scénu kapitoly
	var chapter_scene = "res://scenes/Chapter" + str(current_chapter) + ".tscn"
	
	if ResourceLoader.exists(chapter_scene):
		get_tree().change_scene_to_file(chapter_scene)
	else:
		print("❌ Chyba: Scéna kapitoly neexistuje: ", chapter_scene)
		# Fallback na generickú Chapter scénu
		get_tree().change_scene_to_file("res://scenes/Chapter.tscn")

func _input(event):
	"""Umožní preskočiť intro stlačením klávesy"""
	if event.is_pressed() and (event is InputEventKey or event is InputEventMouseButton):
		print("⏭️ Preskakujem intro")
		is_typing = false
		_finish_intro()
