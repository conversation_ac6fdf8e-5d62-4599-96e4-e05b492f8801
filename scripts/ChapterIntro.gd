extends Control

@onready var video_player: VideoStreamPlayer = $VideoPlayer
@onready var chapter_title: Label = $TextContainer/ChapterTitle
@onready var chapter_description: Label = $TextContainer/ChapterDescription
@onready var fade_overlay: ColorRect = $FadeOverlay
@onready var audio_player: AudioStreamPlayer = $AudioPlayer

var current_chapter: int = 1
var is_typing: bool = false
var typing_speed: float = 0.04  # Rýchlosť typewriter efektu

# Texty pre kapitoly - presne podľa zadania
var chapter_data = {
	1: {
		"title": "KAPITOLA 1: CESTA NA ZÁMOK",
		"description": "Kočiar ťa nesie do srdca Karpát – k <PERSON>áhade, ktor<PERSON> ned<PERSON> Helsingovi spať.",
		"video": "res://assets/kapitoly_videa/1.ogv"
	},
	2: {
		"title": "KAPITOLA 2: BRÁNA ZÁMKU",
		"description": "<PERSON>rána zámku sa otvára len tomu, kto pozná odpovede Rádu.",
		"video": "res://assets/kapitoly_videa/2.ogv"
	},
	3: {
		"title": "KAPITOLA 3: PÁTRANIE V ZÁMKU",
		"description": "Zmiznutie mentora ťa vedie hlbšie – do knižníc, denníkov a zakázaných spisov.",
		"video": "res://assets/kapitoly_videa/3.ogv"
	},
	4: {
		"title": "KAPITOLA 4: TAJNÉ KRÍDLO",
		"description": "Staré múry ukrývajú pasce, elixír i kľúč k Isabelleinej hrobke.",
		"video": "res://assets/kapitoly_videa/4.ogv"
	},
	5: {
		"title": "KAPITOLA 5: KRYPTY",
		"description": "Katakomby dýchajú smrťou – a v ich tieni číha minulosť rodu Báthoryovcov.",
		"video": "res://assets/kapitoly_videa/5.ogv"
	},
	6: {
		"title": "KAPITOLA 6: KONFRONTÁCIA",
		"description": "Grófka sa prebúdza… a ty si poslednou nádejou, že zlo znova neuspeje.",
		"video": "res://assets/kapitoly_videa/6.ogv"
	},
	7: {
		"title": "KAPITOLA 7: ZÁCHRANA MENTORA",
		"description": "Van Helsing žije – no jeho čas sa kráti. Boj ešte nie je celkom dohraný.",
		"video": "res://assets/kapitoly_videa/7.ogv"
	}
}

func _ready():
	# Skryj text na začiatku
	chapter_title.modulate.a = 0.0
	chapter_description.modulate.a = 0.0
	chapter_description.text = ""

	# Spustí intro pre aktuálnu kapitolu z GameManager
	if GameManager:
		start_chapter_intro(GameManager.current_chapter)
	else:
		print("❌ GameManager nedostupný, spúšťam kapitolu 1")
		start_chapter_intro(1)

func start_chapter_intro(chapter_number: int):
	"""Spustí intro pre danú kapitolu"""
	current_chapter = chapter_number
	print("🎬 Spúšťam intro pre kapitolu ", chapter_number)
	
	if not chapter_data.has(chapter_number):
		print("❌ Chyba: Neexistujúce dáta pre kapitolu ", chapter_number)
		_finish_intro()
		return
	
	var data = chapter_data[chapter_number]
	
	# Nastaví titulok
	chapter_title.text = data.title
	
	# Načíta a spustí video
	var video_stream = load(data.video)
	if video_stream:
		video_player.stream = video_stream
		video_player.play()
		
		# Pripojí signál pre koniec videa
		if not video_player.finished.is_connected(_on_video_finished):
			video_player.finished.connect(_on_video_finished)
		
		print("✅ Video načítané: ", data.video)
	else:
		print("❌ Chyba načítania videa: ", data.video)
		_finish_intro()
		return
	
	# Spustí animáciu
	_start_intro_animation(data.description)

func _start_intro_animation(description_text: String):
	"""Spustí animáciu intro s fade efektmi a typewriter textom"""
	var tween = create_tween()
	tween.set_parallel(true)

	# 1. Fade in z čiernej (1.2s)
	tween.tween_property(fade_overlay, "color:a", 0.0, 1.2)

	# 2. Po 1.8s zobraz titulok (1.0s fade in)
	tween.tween_interval(1.8)
	tween.tween_property(chapter_title, "modulate:a", 1.0, 1.0)

	# 3. Po 3.2s začni typewriter efekt pre popis
	await get_tree().create_timer(3.2).timeout
	_start_typewriter_effect(description_text)

func _start_typewriter_effect(text: String):
	"""Spustí typewriter efekt pre popis kapitoly"""
	is_typing = true
	chapter_description.modulate.a = 1.0
	chapter_description.text = ""
	
	print("⌨️ Spúšťam typewriter efekt: ", text)
	
	for i in range(text.length()):
		if not is_typing:
			break
			
		chapter_description.text = text.substr(0, i + 1)
		await get_tree().create_timer(typing_speed).timeout
	
	is_typing = false
	print("✅ Typewriter efekt dokončený")

func _on_video_finished():
	"""Callback po dokončení videa"""
	print("🎬 Video dokončené, končím intro")
	_finish_intro()

func _finish_intro():
	"""Dokončí intro a prejde na kapitolu"""
	print("🎬 Dokončujem intro pre kapitolu ", current_chapter)
	
	# Fade out do čiernej
	var tween = create_tween()
	tween.tween_property(fade_overlay, "color:a", 1.0, 1.0)
	
	await tween.finished
	
	# Prejdi na kapitolu
	_load_chapter()

func _load_chapter():
	"""Načíta príslušnú kapitolu"""
	print("📖 Načítavam kapitolu ", current_chapter)
	
	# Nastaví GameManager
	if GameManager:
		GameManager.current_chapter = current_chapter
		GameManager.story_phase = 0
	
	# Načíta scénu kapitoly
	var chapter_scene = "res://scenes/Chapter" + str(current_chapter) + ".tscn"
	
	if ResourceLoader.exists(chapter_scene):
		get_tree().change_scene_to_file(chapter_scene)
	else:
		print("❌ Chyba: Scéna kapitoly neexistuje: ", chapter_scene)
		# Fallback na generickú Chapter scénu
		get_tree().change_scene_to_file("res://scenes/Chapter.tscn")

func _input(event):
	"""Umožní preskočiť intro stlačením klávesy"""
	if event.is_pressed() and (event is InputEventKey or event is InputEventMouseButton):
		print("⏭️ Preskakujem intro")
		is_typing = false
		_finish_intro()
